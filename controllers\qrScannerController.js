const qr_scanner = function () { }
require('dotenv').config();
const _ = require("underscore")
const moment = require("moment")
const mapper = require("../db-config/connection")
const queryBuilder = require("../db-config/queryBulding");
const message = require("./messages/ApiMessages")
const common = require("../controllers/commonController")

qr_scanner.insert = async (req, res) => {
    try {
        if (req.body.encrypyData) {
            let decryptData = await common.decrypt(req.body.encrypyData)
            let getQrDetails = await queryBuilder("generated_qr_list", "findBy", { "id": decryptData.id })
            if (getQrDetails && getQrDetails.length > 0) {
                if (getQrDetails && getQrDetails[0] && getQrDetails[0]["is_scanned"] == 0 && getQrDetails[0]["is_redeemed"] == 0) {
                    let params = {
                        "user_id":req.usersData.user_id,
                        "product_id ":decryptData.product_id,
                        "qr_list_id":decryptData.id,
                        "reward_points": getQrDetails[0].reward_points,
                        "location": req.body.location || "",
                        "scanned_date":moment(new Date()).format("YYYY-MM-DD HH:MM:SS"),
                        "is_approved":0
                    }
                    let params1 = {
                        "user_id":req.usersData.user_id,
                        "product_id ":decryptData.product_id,
                        "reward_points": `+${getQrDetails[0].reward_points}`,
                        "scanned_dt":params.scanned_date
                    }
                    let query = await queryBuilder("scanned_qr", "insert", params)
                    let update = await queryBuilder("generated_qr_list", "update", { "is_scanned": 1, "id": getQrDetails[0].id })
                    let transaction_history = await queryBuilder("transaction_history", "insert", params1)
                    return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.insert })
                } else {
                    return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.qrExpaired })
                }
            }
            else {
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validQrListId })
            }
        } else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams })
        }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}


qr_scanner.get = async (req, res) => {
    try {
        let filterColumns = "scanned_qr.*,products.name AS product_name"
        let joins = {
            "query": [
                {
                    "join": "JOIN",
                    "tableName": "products,scanned_qr",
                    "columns": "id =,product_id",
                }
            ]
        }
        let query = await queryBuilder("scanned_qr", "find", "", filterColumns, joins)
        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query, "message": "" })
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

/**users */
qr_scanner.approvalsById = async (req, res) => {
    try {
        let filterColumns = "SUM(reward_points) AS Rewards_points_for_Approval"
        let total = await queryBuilder("scanned_qr", "findBy", { "user_id": `${req.usersData.user_id}` }, filterColumns)
        let reedemed = await queryBuilder("scanned_qr", "findBy", { "is_approved": 2, "user_id": `${req.usersData.user_id}` }, filterColumns)
        let waitingForApproval = await queryBuilder("scanned_qr", "findBy", { "is_approved": 2, "user_id": `${req.usersData.user_id}` }, filterColumns)
        let resp = {
            "Rewards_points_for_Approval": waitingForApproval[0].Rewards_points_for_Approval ? waitingForApproval[0].Rewards_points_for_Approval : 0,
            "total_reward_points": total[0].Rewards_points_for_Approval ? total[0].Rewards_points_for_Approval : 0,
            "rewards_points_reedemed": reedemed[0].Rewards_points_for_Approval ? reedemed[0].Rewards_points_for_Approval : 0
        }
            let balance = resp.total_reward_points - resp.rewards_points_reedemed
            resp["balance_reward_points"] = balance
            return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": resp, "message": "" })
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

/**admin */
qr_scanner.getUsersById = async (req, res) => {
    try {
        let filterColumns = "SUM(reward_points) AS Rewards_points_for_Approval"
        let approval = await mapper(`SELECT ${filterColumns} FROM scanned_qr WHERE is_approved IN (0, 2) AND user_id =${req.body.user_id}`)
        let total = await queryBuilder("scanned_qr", "findBy", { "user_id": `${req.body.user_id}` }, filterColumns)
        let reedemed = await queryBuilder("scanned_qr", "findBy", { "is_approved": 2, "user_id": `${req.body.user_id}` }, filterColumns)
        let waitingForApproval = await queryBuilder("scanned_qr", "findBy", { "is_approved": 2, "user_id": `${req.body.user_id}` }, filterColumns)
        let resp = {
            "reward_granted": approval["data"][0].Rewards_points_for_Approval ? approval["data"][0].Rewards_points_for_Approval : 0,
            "Rewards_points_for_Approval": waitingForApproval[0].Rewards_points_for_Approval ? waitingForApproval[0].Rewards_points_for_Approval : 0,
            "total_reward_points": total[0].Rewards_points_for_Approval ? total[0].Rewards_points_for_Approval : 0,
            "rewards_points_reedemed": reedemed[0].Rewards_points_for_Approval ? reedemed[0].Rewards_points_for_Approval : 0
        }
            let balance = resp.total_reward_points - resp.rewards_points_reedemed
            resp["balance_reward_points"] = balance
            return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": resp, "message": "" })
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}
module.exports = qr_scanner