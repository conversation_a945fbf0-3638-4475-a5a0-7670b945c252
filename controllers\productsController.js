let products = function () { }
const _ = require("underscore")
const path = require("path")
const fs = require("fs")
require('dotenv').config();
const mapper = require("../db-config/connection")
const multer = require("multer")
const commonController = require("./commonController")
const queryBuilder = require("../db-config/queryBulding");
const queryBuilderinsert = require("../db-config/queryBuldinginsert");
const message = require("./messages/ApiMessages")

products.store = async(req,res) => {
    try {
        if(req.body.modal && req.body.name){
            if(req.file){
            req.body["image"] = await commonController.ImageUpload("products_images",req.file)
            let filterData = req.body
            let query = await queryBuilder("products", "insert",filterData)
            return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.insert })
        }
        else{
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.noFile })
        }
        }else{
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams })
        }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

products.get = async(req,res) => {
    try {
        let limit = {
            "page" : req.body.params.page ? parseInt(req.body.params.page) : 1,
            "limit" : 10
        }
        let finalresp = []
        let filterColumns = "products.*,model.model AS model_name"
        let joins = {
            "query": [
                {
                    "join": "JOIN",
                    "tableName": "model,products",
                    "columns": "id =,modal",
                }
            ]
        }
        let countQuery = await mapper(`SELECT COUNT(*) AS total_count FROM products`);
        let query = await queryBuilderinsert("products", "find","",filterColumns,joins,limit)
        if(query.data && query.data.length > 0){
            _.each(query.data,function(data,_idx){
                data["imageUrl"] = `${process.env.url}/admin/images/products/${data.image}`
                finalresp.push(data)
            })
        }
        else{
            finalresp = []
        }
        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": finalresp,"total_count":countQuery.data[0].total_count, "message": "" })
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}


products.getByModelId = async(req,res) => {
    try {
        if(req.body && req.body.modal){
        let filterData = req.body
        let finalresp = []
        let query = await queryBuilder("products", "findBy",filterData)
        if(query && query.length > 0){
            _.each(query,function(data,_idx){
                data["imageUrl"] = `${process.env.url}/admin/images/products/${data.image}`
                finalresp.push(data)
            })
        }
        else{
            finalresp = []
        }
        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": finalresp, "message": "" })
    }
    else{
        return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams })
    }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

products.getProductsById = async(req,res) => {
    try {
        if(req.body.product_id){
        let filterColumns = "products.*,model.model AS model_name"
        let joins = {
            "addingQuery": `WHERE products.id =${req.body.product_id}`,
            "query": [
                {
                    "join": "JOIN",
                    "tableName": "model,products",
                    "columns": "id =,modal",
                }
            ]
        }
        let query = await queryBuilder("products", "find","",filterColumns,joins)
        if(query && query.length > 0){
            query[0]["imageUrl"] = `${process.env.url}/users/images/products/${query[0]["image"]}`
            return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query[0], "message": "" })
        }
        else{
            query = []
            return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query, "message": "" })
        }
    }
    else{
        return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams })
    }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

products.usersProductById = async(req,res) => {
    try {
        if(req.body.encrypyData){
        let decryptData = await commonController.decrypt(req.body.encrypyData)
        let filterColumns = "products.*,model.model AS model_name"
        let joins = {
            "addingQuery": `WHERE products.id =${decryptData.product_id}`,
            "query": [
                {
                    "join": "JOIN",
                    "tableName": "model,products",
                    "columns": "id =,modal",
                }
            ]
        }
        let query = await queryBuilder("products", "find","",filterColumns,joins)
        if(query && query.length > 0){
            query[0]["imageUrl"] = `${process.env.url}/users/images/products/${query[0]["image"]}`
            query[0]["reward_points"] = decryptData.reward_points
            return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query[0], "message": "" })
        }
        else{
            query = []
            return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query, "message": "" })
        }
    }
    else{
        return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams })
    }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}


products.bulkUpload = async (req, res) => {
    try {
        let finalResp = [];
        let finalObj = []
        let params;
        if (req.files) {
            if (req.files.excel[0].mimetype == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && req.files.image[0].mimetype == 'application/zip') {
                let excelData = await commonController.excel2Json(req.files.excel[0].buffer);
                let unzipData = await commonController.unzip(req.files.image);
                if (excelData && excelData.length > 0) {
                    for (let data of excelData) {
                        let imagePath = path.join(__dirname, `/images/imagesUpload/productsImages/${data.IMAGE}`);
                        let imageExists = await commonController.checkImageExists(imagePath);
                        if (imageExists) {
                            let model = await queryBuilder("model", "findBy",{"model":data.MODEL.trim()});
                            let imagetoBuffer = await commonController.imageToBuffer(imagePath)
                            imagetoBuffer = {
                                "buffer":imagetoBuffer
                            }
                            let imageSplit = data.IMAGE.split(".")
                            let mimetype = `application/${imageSplit[1]}`
                            let image = await commonController.ImageUpload("products_images", imagetoBuffer,mimetype);
                            if (model && model.length > 0) {
                                params = {
                                    "name": data.PRODUCT_NAME,
                                    "modal": model[0].id,
                                    "image": image
                                };
                            } else {
                                let resp = await queryBuilderinsert("model", "insert", { "model": data.MODEL.trim() });
                                params = {
                                    "name": data.PRODUCT_NAME,
                                    "modal": resp.lastInsertedId,
                                    "image": image
                                };
                            }
                            finalObj.push(params)
                        } else {
                            finalResp.push(data);
                        }
                    }
                let resp = await commonController.insertQueryBuilder(finalObj)
                await queryBuilderinsert("products","bulkInsert",resp)
                await fs.rmSync(path.join(__dirname,`images/imagesUpload/${unzipData}`), { recursive: true });
                    return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.insert });
                } else {
                    return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.nodatainexcel });
                }
            } else {
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.files });
            }
        } else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.excelfileandzipFile });
        }
    } catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError });
    }
};

module.exports = products