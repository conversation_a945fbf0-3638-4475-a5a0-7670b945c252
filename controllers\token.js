let usersController = function () { };

const moment = require("moment");
const _jwt = require("jsonwebtoken")
require('dotenv').config();
const queryBuilder = require("../db-config/queryBulding");
const commonController = require("./commonController")
const message = require("./messages/ApiMessages");
const { response } = require("../routes/users");


usersController.generate_Otp = async (req, res) => {
    try {
        if (req.body.mobile){
            if(await commonController.validMobileno(req.body.mobile)){
            let query = await queryBuilder("users","findBy",req.body)
            if(query && query.length > 0){
                if(query && query[0] && query[0].role_id == 1){
                await commonController.otpGenerating(req.body.mobile,query[0].role_id)
                return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.generated })
            }
            else{
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.invalidMobile }) 
            }
            }
            else{
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.usernotExists })  
            }
        }
        else{
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.mobileValidation })   
        }
    }
        else{
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.mobile })   
        }
    } catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

usersController.validate_Otp = async (req, res) => {
    try {
        if (req.body.mobile) {
            if(req.body.otp){
            let query = await queryBuilder("users", "findBy", {"mobile":req.body.mobile})
            if (query && query.length > 0) {
                let resp = await commonController.ValidateOtp(req.body.mobile,req.body.otp)
                if(resp && resp.message && resp.message == "OTP expired"){
                    return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.otpExpaired })  
                }
                else if(resp && resp.message && resp.message == "OTP not match"){
                    return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.otpValidation })
                }
                else{
                    let params = { 
                        "mobile": req.body.mobile,
                        "role_id":query[0].role_id,
                        "user_id":query[0].id,
                        "role":"ADMIN"
                    }
                    let generateToken = await _jwt.sign(params, process.env.secreatKey)
                    return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "Token": generateToken, "message": message.otpValidated })
            }
        }
            else {
                return res.status(403).json({ "statusCode": 403, "status": "SUCCESS", "data": query, "message": message.usernotExists })
            }
        }
        else{
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.otpValidation })
        }
        }
        else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams })
        }
    } catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

/**users */
usersController.verify_Token = async (req, res, next) => {
    try {
        if (req.headers["token"]) {
            let resp = _jwt.verify(req.headers["token"], process.env.secreatKey)
            if (!resp) {
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.notvalidToken })
            }
            else {
                if(resp.mobile && resp.role_id && resp.user_id){
                let DataResp = await queryBuilder("users", "findBy", {"id":resp.user_id,"mobile":resp.mobile,"role_id":resp.role_id})
                if(DataResp && DataResp.length > 0){
                    req.usersData = resp
                    next()
                }
                else{
                    return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.usernotExists }) 
                }
            }else{
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.notvalidToken })
            }
            }
        }
        else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.notprovide })
        }
    }
    catch (error) {
        if (error && error.message == "invalid signature") {
            return res.status(401).json({ "statusCode": 401, "status": "FAIL", "data": [], "message": message.notvalidToken })
        }
        else {
            return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
        }
    }
}

usersController.verify_Token_admin = async (req, res, next) => {
    try {
        if (req.headers["token"]) {
            let resp = _jwt.verify(req.headers["token"], process.env.secreatKey)
            if (!resp) {
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.notvalidToken })
            }
            else {
                if(resp.mobile && resp.role_id && resp.role && resp.user_id && resp.role == "ADMIN"){
                let DataResp = await queryBuilder("users", "findBy", {"id":resp.user_id,"mobile":resp.mobile,"role_id":resp.role_id})
                if(DataResp && DataResp.length > 0){
                    req.adminData = resp
                    next()
                }
                else{
                    return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.usernotExists }) 
                }
            }else{
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.notvalidToken })
            }
            }
        }
        else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.notprovide })
        }
    }
    catch (error) {
        if (error && error.message == "invalid signature") {
            return res.status(401).json({ "statusCode": 401, "status": "FAIL", "data": [], "message": message.notvalidToken })
        }
        else {
            return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
        }
    }
}

usersController.no_api = async (req, res, next) => {
    return res.status(404).json({ "statusCode": 404, "status": "FAIL", "data": [], "message": message.noApi })
}

module.exports = usersController;