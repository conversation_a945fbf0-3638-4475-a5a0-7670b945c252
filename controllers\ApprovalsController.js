const approvals = function () { }
const queryBuilder = require("../db-config/queryBulding");
const message = require("./messages/ApiMessages")
const _ = require("underscore")
const moment = require("moment")
const mapper = require("../db-config/connection")

approvals.insert = async (req, res) => {
    try {
        if (req.body.transaction_id) {
            let transactions = req.body.transaction_id.split(",");
            for (let data of transactions) {
                let qrRedeemedData = await queryBuilder("redeemed_qr", "findBy", { "transaction_id": data });
                if (qrRedeemedData && qrRedeemedData.length > 0) {
                    for (let data1 of qrRedeemedData) {
                        let date = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
                        let redeemedUpdate = await mapper(`UPDATE redeemed_qr SET approvals = 1, updated_at = '${date}' WHERE transaction_id = '${data1.transaction_id}'`);
                        let scannerUpdate = await mapper(`UPDATE scanned_qr SET is_approved = 1, approved_date = '${date}', updated_at = '${date}' WHERE qr_list_id = '${data1.qr_list_id}' AND is_approved = 2`);
                    }
                }
            }
            return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.insert });
        } else if (req.body.user_id) {
            let qrScannedData = await queryBuilder("scanned_qr", "findBy", { "user_id": req.body.user_id, "is_approved": 2 });
            if (qrScannedData && qrScannedData.length > 0) {
                for (let data1 of qrScannedData) {
                    let date = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
                    let redeemedUpdate = await mapper(`UPDATE redeemed_qr SET approvals = 1, updated_at = '${date}' WHERE qr_list_id = '${data1.qr_list_id}'`);
                    let scannerUpdate = await mapper(`UPDATE scanned_qr SET is_approved = 1, approved_date = '${date}', updated_at = '${date}' WHERE qr_list_id = '${data1.qr_list_id}' AND is_approved = 2`);
                }
            }
            return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.insert });
        } else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams });
        }
    } catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError });
    }
}


approvals.get = async (req, res) => {
    try {
        if(req.body.transaction_id){
            let filterData = "redeemed_qr.*,products.name AS product_name,users.name As user_name,roles.name AS role_name"
            let joins = {
                "addingQuery": `WHERE approvals =0 AND redeemed_qr.transaction_id ='${req.body.transaction_id}'`,
                "query": [
                    {
                        "join": "JOIN",
                        "tableName": "generated_qr_list,redeemed_qr",
                        "columns": "id =,qr_list_id",
                    },
                    {
                        "join": "JOIN",
                        "tableName": "users,redeemed_qr",
                        "columns": "id =,redeemed_by", 
                    },
                    {
                        "join": "JOIN",
                        "tableName": "roles,users",
                        "columns": "id =,role_id", 
                    },
                    {
                        "join": "JOIN",
                        "tableName": "products,generated_qr_list",
                        "columns": "id =,product_id",
                    }
                ]
            };
            let qrreedmeddata = await queryBuilder("redeemed_qr","find","",filterData,joins)
            return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": qrreedmeddata, "message": "" })

        }else{
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams })
        }
    } catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

module.exports = approvals;