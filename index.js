const express = require('express');
const router = express()
const bodyParser = require("body-parser")
const config = require("./db-config/config")
const cors = require('cors')

router.use(bodyParser.json());
router.use(bodyParser.urlencoded({ extended: true }));
router.use(cors());

const users = require("./routes/users")
const admin = require("./routes/admin")

router.get("/",(req,res)=>{
    res.send("Api Working Fine")
})
router.use("/admin",admin)
router.use("/users",users)


router.listen(config.port,()=>{
    console.log(`http://localhost:${config.port}`)
})