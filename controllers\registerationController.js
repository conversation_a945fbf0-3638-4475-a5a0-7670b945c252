let registerController = function () { };

const moment = require("moment");
require('dotenv').config();
const _jwt = require("jsonwebtoken")
const commonController = require("./commonController")
const queryBuilder = require("../db-config/queryBulding");
const queryBuilderinsert = require("../db-config/queryBuldinginsert");
const message = require("./messages/ApiMessages")

/**users register */
registerController.registeration = async (req, res) => {
    try {
        if (req.body.mobile && !req.body.otp && !req.body.role_id) {
            if (await commonController.validMobileno(req.body.mobile)) {
                let resp = await queryBuilder("users", "findBy", { "mobile": req.body.mobile })
                if (resp && resp.length > 0) {
                    if (resp && resp[0] && resp[0].role_id != 1) {
                        await commonController.otpGenerating(req.body.mobile,resp[0].role_id)
                        return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.generated })
                    }
                    else{
                        return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.roleIdExists })
                    }
                }
                else {
                    await commonController.otpGenerating(req.body.mobile,2)
                    return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.generated })
                }
            }
            else {
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.mobileValidation })
            }
        }
        else if (req.body.mobile && req.body.otp && req.body.role_id) {
            let query = await queryBuilder("users", "findBy", { "mobile": req.body.mobile })
            if (query && query.length > 0) {
                if (query && query[0] && query[0]["role_id"] == req.body.role_id) {
                    let resp = await commonController.ValidateOtp(req.body.mobile, req.body.otp)
                    if (resp && resp.message && resp.message == "OTP expired") {
                        return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.otpExpaired })
                    }
                    else if (resp && resp.message && resp.message == "OTP not match") {
                        return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.otpValidation })
                    }
                    else {
                        let tokenParams = {
                            "mobile": req.body.mobile,
                            "role_id": req.body.role_id,
                            "user_id": query[0].id
                        }
                        let generateToken = await _jwt.sign(tokenParams, process.env.secreatKey)
                        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query[0], "Token": generateToken, "message": message.otpValidated })
                    }
                }
                else {
                    return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.roleIdExists })
                }
            }
            else {
                    let resp = await commonController.ValidateOtp(req.body.mobile, req.body.otp)
                    if (resp && resp.message && resp.message == "OTP expired") {
                        return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.otpExpaired })
                    }
                    else if (resp && resp.message && resp.message == "OTP not match") {
                        return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.otpValidation })
                    }
                    else {
                        let params = {
                            "name":"",
                            "email_id":"",
                            "address":"",
                            "location":"",
                            "profile":"",
                            "mobile": req.body.mobile,
                            "role_id": req.body.role_id
                        }
                        let query1 = await queryBuilderinsert("users", "insert", params)
                        let query2 = await queryBuilder("users","findBy",{"id":query1.lastInsertedId})
                        let tokenParams = {
                            "mobile": req.body.mobile,
                            "role_id": req.body.role_id,
                            "user_id": query2[0].id
                        }
                        let generateToken = await _jwt.sign(tokenParams, process.env.secreatKey)
                        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query2[0], "Token": generateToken, "message": message.otpValidated })
                    }
            }
        }
        else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validetails })
        }
    } catch (error) {
        console.log(error)
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

registerController.update = async (req, res) => {
    try {
        let resp = await queryBuilder("users", "findBy", { "mobile": req.usersData.mobile })
        if (resp && resp.length > 0) {
            let params = {
                "id": resp[0]["id"],
                "location": req.body.location || null,
                "name": req.body.name || null,
                "address": req.body.address || null
            }
            let update = await queryBuilder("users", "update", params)
            return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.update })
        }
        else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.notFound })
        }
    } catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

module.exports = registerController;