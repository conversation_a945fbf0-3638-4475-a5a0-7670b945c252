let users = function () { };
require('dotenv').config();
const queryBuilder = require("../db-config/queryBulding")
const message = require("./messages/ApiMessages")


users.get = async (req, res) => {
    try {
            let query = await queryBuilder("users", "findBy", {"mobile":req.usersData.mobile})
            if (query && query.length > 0) {
                return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query[0], "message": "" })
            }
            else {
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.mobile })
            }
        }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

module.exports = users;