const mapper = require("../db-config/connection")
const _ = require("underscore")

let queries = {
    "find": `SELECT ?filterColumns FROM ?module`,
    "findBy": `SELECT ?filterColumns FROM ?module WHERE ?keys`,
    "insert": `INSERT INTO ?module(?keys) VALUES (?values)`,
    "update": `UPDATE ?module SET ?setValues WHERE id = ?id`
}

module.exports = async (module, method, _body, filterColumns, _joins) => {
    try {
        let finalResp = queries[method];
        if (method == 'find') {
            if (filterColumns && filterColumns.length > 0) {
                finalResp = finalResp.replace(/\?filterColumns/g, filterColumns).replace(/\?module/g, module)
            }
            else {
                finalResp = finalResp.replace(/\?filterColumns/g, "*").replace(/\?module/g, module)
            }
        }
        else if (method == 'findBy') {
            let whereClause = '';
            if (filterColumns && filterColumns.length > 0) {
                finalResp = finalResp.replace(/\?filterColumns/g, filterColumns).replace(/\?module/g, module)
            }
            else {
                finalResp = finalResp.replace(/\?filterColumns/g, "*").replace(/\?module/g, module)
            }
            Object.keys(_body).forEach((key, index) => {
                if (index > 0) {
                    whereClause += ' AND ';
                }
                whereClause += key + ' = ' + (typeof _body[key] === 'string' ? `'${_body[key]}'` : _body[key]);
            });
            finalResp = finalResp
                .replace(/\?keys/g, whereClause).replace(/\?module/g, module)
        }
        else if (method == "update") {
            let setValues = '';
            let isFirst = true;
            Object.keys(_body).forEach((key) => {
                if (key !== 'id') {
                    if (!isFirst) {
                        setValues += ', ';
                    } else {
                        isFirst = false;
                    }
                    setValues += key + ' = ' + (typeof _body[key] === 'string' ? `'${_body[key]}'` : _body[key]);
                }
            });
            finalResp = finalResp
                .replace(/\?setValues/g, setValues)
                .replace(/\?id/g, _body.id)
                .replace(/\?module/g, module);
        }
        else {
            finalResp = finalResp
                .replace(/\?keys/g, Object.keys(_body))
                .replace(/\?values/g, Object.values(_body).map(value => typeof value === 'string' ? `'${value}'` : value).join(', '))
                .replace(/\?module/g, module)
        }
        if (_joins && Array.isArray(_joins.query)) {
            _joins.query.forEach(join => {
                let tables = join.tableName.split(",")
                let columns = join.columns.split(",")
                finalResp += ` ${join.join} ${tables[0]} ON ${tables[0]}.${columns[0]} ${tables[1]}.${columns[1]}`
            });
            if(_joins.addingQuery){
            finalResp += ` ${_joins.addingQuery}`
            }
            finalResp += ` ORDER BY ${module}.id DESC`
        }
        else if(method == 'find' || method == 'findBy'){
            finalResp += ` ORDER BY ${module}.id DESC`
        }
        let response = await mapper(finalResp);
        return response.data;
    } catch (error) {
        throw new Error(error.data);
    }
};