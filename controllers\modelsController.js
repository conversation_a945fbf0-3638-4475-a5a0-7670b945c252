const models = function () { }
const moment = require("moment")
const queryBuilder = require("../db-config/queryBulding");
const message = require("./messages/ApiMessages")

models.store = async (req, res) => {
    try {
        if (req.body.model) {
            let filterData = req.body
            let query = await queryBuilder("model", "insert", filterData)
            return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.insert })
        } else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams })
        }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}


models.get = async (req, res) => {
    try {
        let query = await queryBuilder("model", "find")
        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query, "message": "" })
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}


models.getById = async (req, res) => {
    try {
        let filterData = {
            "id": req.params.id
        }
        let query = await queryBuilder("model", "findBy", filterData)
        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query, "message": "" })
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}



models.update = async (req, res) => {
    try {
        if (req.body.model) {
            req.body["id"] = req.params.id
            req.body["updated_at"] = moment(new Date()).format("YYYY-MM-DD HH:MM:SS")
            let filterData = req.body
            let query = await queryBuilder("model", "update", filterData)
            return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.update })
        } else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams })
        }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}
module.exports = models