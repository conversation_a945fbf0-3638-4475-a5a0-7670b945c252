let qr_code = function () { }
const _ = require("underscore")
const qr = require("./commonController")
const mapper = require("../db-config/connection")
const queryBuilder = require("../db-config/queryBulding");
const message = require("./messages/ApiMessages")
const queryBuilderlatest = require("../db-config/queryBuldinginsert");

qr_code.qr_generator = async (req, res) => {
    try {
        let finalResp= []
        if (req.body.product_id && req.body.modal && req.body.quantity && req.body.reward_points && req.body.quantity > 0) {
        req.body["reward_points"] = parseInt(req.body["reward_points"])
            let query = await queryBuilderlatest("qr_list", "insert", req.body)
            let qrGet = await queryBuilder("qr_list", "findBy", { "id": query.lastInsertedId})
            if (qrGet && qrGet.length > 0) {
                let qrGenerate = await qr.QrcodeGeneratingmultiple(qrGet[0])
                let filterColumns = "generated_qr_list.*,DATE_FORMAT(generated_qr_list.created_at, '%d-%b-%Y') AS created,products.name AS product_name"
                let joins = {
                    "query": [
                        {
                            "join": "JOIN",
                            "tableName": "products,generated_qr_list",
                            "columns": "id =,product_id",
                        }
                    ]
                }
                let resp = await queryBuilder("generated_qr_list", "find","",filterColumns,joins)
                _.each(resp,function(data){
                    data["imageUrl"] = `${process.env.url}/admin/images/qr-images/${data["image"]}`
                    finalResp.push(data)
                })
                return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": finalResp, "message": "" })
            } else {
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validQrListId })
            }
        }
        else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams })
        }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}


qr_code.get = async (req, res) => {
    try {
        let limit = {
            "page" : req.body.params.page ? parseInt(req.body.params.page) : 1,
            "limit" : 10
        }
        let filterColumns = "DATE_FORMAT(qr_list.created_at, '%d-%b-%Y') AS created,qr_list.*,products.name AS product_name"
        let joins = {
            "query": [
                {
                    "join": "JOIN",
                    "tableName": "products,qr_list",
                    "columns": "id =,product_id",
                }
            ]
        }
        let countQuery = await mapper(`SELECT COUNT(*) AS total_count FROM qr_list`);
        let query = await queryBuilderlatest("qr_list", "find", "", filterColumns, joins,limit)
        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query.data,"total_count":countQuery.data[0].total_count, "message": "" })
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

qr_code.images = async (req, res) => {
    try {
        let limit = {
            "page" : req.body.params.page ? parseInt(req.body.params.page) : 1,
            "limit" : 10
        }
        let finalResp = []
        let filterColumns = "DATE_FORMAT(generated_qr_list.created_at, '%d-%b-%Y') AS created,generated_qr_list.*,products.name AS product_name"
        let joins = {
            "query": [
                {
                    "join": "JOIN",
                    "tableName": "products,generated_qr_list",
                    "columns": "id =,product_id",
                }
            ]
        }
            let countQuery = await mapper(`SELECT COUNT(*) AS total_count FROM generated_qr_list`);
            let qrGet = await queryBuilderlatest("generated_qr_list", "find","",filterColumns, joins,limit)
            if (qrGet.data && qrGet.data.length > 0) {
                _.each(qrGet.data,function(data){
                    data["imageUrl"] = `${process.env.url}/admin/images/qr-images/${data["image"]}`
                    finalResp.push(data)
                })
                return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": finalResp,"total_count":countQuery.data[0].total_count, "message": "" })
            } else {
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validQrListId })
            }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

qr_code.images_get = async (req, res) => {
    try {
        let finalResp = []
        if(req.body.qr_list_id){
        let filterColumns = "generated_qr_list.id,generated_qr_list.image"
        let query = await queryBuilder("generated_qr_list", "findBy",req.body,filterColumns)
        if(query && query.length > 0){
            _.each(query,function(data,idx){
                data["imageUrl"] = `${process.env.url}/admin/images/qr-images/${data["image"]}`
                finalResp.push(data)
            })
        }
        else{
            finalResp= []
        }
        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": finalResp, "message": "" })
        }
        else{
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams }) 
        }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}
module.exports = qr_code