let dashboard = function () { }
const _ = require("underscore")
const mapper = require("../db-config/connection");
const message = require("./messages/ApiMessages")

dashboard.get = async (req, res) => {
    try{
        let qr_count = await mapper(`SELECT COUNT(*) AS record_count FROM generated_qr_list`)
        let customers = await mapper(`SELECT COUNT(*) AS record_count FROM users WHERE role_id = 3`)
        let resp = {
            "qrCount": qr_count.data[0].record_count,
            "customers": customers.data[0].record_count
        }
        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": resp, "message": "" })
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}
module.exports = dashboard