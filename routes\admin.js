const express = require('express');
const multer = require('multer');
require('dotenv').config();
const registeration = require("../controllers/registerationController")
const token = require('../controllers/token');
const qr = require("../controllers/qr-codeController");
const plumbers = require("../controllers/plumbersController")
const customers = require("../controllers/customersController")
const model = require("../controllers/modelsController")
const products = require("../controllers/productsController")
const images = require("../controllers/imageController")
const redeem = require("../controllers/qrReedmedController")
const approvals = require("../controllers/ApprovalsController")
const scanner = require("../controllers/qrScannerController")
const dashboard = require("../controllers/dashboardController")

const router = express();
const upload = multer();
/**otp*/
router.route('/generate_Otp').post(token.generate_Otp);
 
router.route('/validate_Otp').post(token.validate_Otp);

/**image view */

router.route('/:imagePath/:image').get(images.get)
/***
 * excel download
 * no need of token for exports of excels data
 */
router.route('/plumbers_exports').get(plumbers.plumbers_exports)

router.route('/customers_exports').get(customers.customers_exports)
/**
 * verifying token for all apies
 */
router.all("*/",token.verify_Token_admin)
/**dashboard */

router.route('/dashboard').post(dashboard.get)

/**qr-code*/
router.route('/qr-generator').post(qr.qr_generator)

router.route('/qr-get').post(qr.get)

router.route('/qr-print').post(qr.images_get)

router.route('/get_generated_qr_products').post(qr.images)
/**plumbers*/
router.route('/plumbers').post(plumbers.get)

router.route('/plumbers/import').post(plumbers.upload)

/**customers*/
router.route('/customers').post(customers.get)

router.route('/customers/import').post(customers.upload)

/**models */
router.route('/model/store').post(model.store)

router.route('/models').post(model.get)

router.route('/models/:id').get(model.getById)

router.route('/model/edit/:id').post(model.update)

/**products */
router.route('/products/store').post(upload.single('image'),products.store)

router.route('/products/bulkUpload').post(upload.fields([
    { name: 'image', maxCount: 1 },
    { name: 'excel', maxCount: 1 }
]),products.bulkUpload)

router.route('/products').post(products.get)

router.route('/products/get').post(products.getProductsById)

router.route('/product/model').post(products.getByModelId)

/**redeem */

router.route("/redeem").post(redeem.get)

/**scanners */
router.route("/scanners").post(approvals.get)

router.route('/scanners/get').post(scanner.getUsersById)

/**approvals */
router.route("/approval/store").post(approvals.insert)

router.route("/get_Items_By_Transaction_id").post(approvals.get)

router.all('*',token.no_api)

module.exports = router;