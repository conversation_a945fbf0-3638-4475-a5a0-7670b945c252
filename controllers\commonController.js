require('dotenv').config();
let secreatKey = "%&$$*&$#@!#)^%"
const fs = require("fs")
const path = require("path")
const qr = require('qrcode')
const excel = require("exceljs");
const axios = require("axios")
const XLSX = require('xlsx');
const mapper = require("../db-config/connection")
const queryBuilder = require("../db-config/queryBulding")
const queryBuilderlatest = require("../db-config/queryBuldinginsert");
const CryptoJS = require("crypto-js");
const unzipper = require('unzipper');
const _ = require('underscore');


/*json to excel Generator*/
async function excelDataConvert(_jsonData, res, _type) {
    return new Promise(async (resolve, reject) => {
        try {
            let workbook = new excel.Workbook();
            let worksheet = workbook.addWorksheet(`${_type}`);
            const headers = Object.keys(_jsonData[0]);
            worksheet.addRow(headers);

            _jsonData.forEach(obj => {
                const row = [];
                headers.forEach(header => {
                    row.push(obj[header]);
                });
                worksheet.addRow(row);
            });

            res.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            res.setHeader("Content-Disposition", `attachment; filename=${_type}.xlsx`);
            await workbook.xlsx.write(res);
            res.end();
            resolve();
        } catch (error) {
            reject(error);
        }
    });
}

/*qr code generating*/
async function QrcodeGenerating(_body) {
    return new Promise((reslove, reject) => {
        const options = {
            errorCorrectionLevel: 'H',
            type: 'png',
            size: 10,
            quality: 0.92,
            margin: 0,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        };
        let encrypy  = CryptoJS.AES.encrypt(JSON.stringify(_body),secreatKey).toString();
        qr.toDataURL(encrypy, options, async (err, url) => {
            if (err) {
                reject(err)
            }
            else {
                reslove(url)
            }
        })
    })
}

function imageUpload(filePath, bufferData, fileName) {
    return new Promise(async (reslove, reject) => {
        await fs.writeFile(`${filePath}/${fileName}`, bufferData, 'base64', (err) => {
            if (err) {
                reject(true)
            }
            else {
                reslove(false)
            }
        });
    })
}

/**image upload */
function ImageUpload(paths, _body,mimeType) {
    let mimetype;
    return new Promise(async (reslove, reject) => {
        let filePath;
        let finalFileName;
        let fileName = `${new Date().getTime()}`
        if (paths == 'products_images') {
            filePath = path.join(__dirname, `images/products/`);
        } else {
            filePath = path.join(__dirname, `images/products/`);
        }
        if (_body) {
            const bufferData = _body.buffer.toString('base64');
            if(mimeType){
                mimetype = mimeType.split("/")
            }
            else{
                mimetype = _body.mimetype.split("/")
            }
            finalFileName = `${fileName}.${mimetype[1]}`
            if (fs.existsSync(filePath)) {
                await imageUpload(filePath, bufferData, finalFileName)
            } else {
                await fs.mkdirSync(filePath, { recursive: true });
                resp = await imageUpload(filePath, bufferData, finalFileName)
            }
            reslove(finalFileName)
        }
        else {
            finalFileName = "no_image.png"
            reslove(finalFileName)
        }
    })
}

/**multiple qr codes generating */
async function QrcodeGeneratingmultiple(_body) {
    for (let i = 1; i <= _body.quantity; i++) {
        let params1 = {
            "qr_list_id":_body.id,
            "product_id": _body.product_id,
            "reward_points": _body.reward_points,
            "is_scanned" : 0,
            "is_redeemed" : 0,
            "image" : `${new Date().getTime()}.png`
        }
        let query = await queryBuilderlatest("generated_qr_list", "insert", params1)
        let params = {
            "product_id": _body.product_id,
            "modal": _body.modal,
            "reward_points": _body.reward_points,
            "quantity": _body.quantity,
            "id":query.lastInsertedId
        }
        let resp = await QrcodeGenerating(params)
        let filePath = path.join(__dirname, 'images', 'qr-images', params1["image"]);
        const imageBuffer = Buffer.from(resp.split(",")[1], 'base64');
        let save = fs.writeFileSync(filePath, imageBuffer);
    }
}

/**excel to json convertor */
async function excel2Json(_body,_table) {
    return new Promise((reslove, reject) => {
        const bufferData = Buffer.from(_body.buffer)
        const workbook = XLSX.read(bufferData, { type: 'buffer' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        reslove(jsonData)
    })
}

async function validMobileno(_mobileno){
    const mobileNumberPattern = /^[0-9]{10}$/;
    return mobileNumberPattern.test(_mobileno);
}

async function decrypt(_encrypt){
    let decrypt  = await CryptoJS.AES.decrypt(_encrypt, secreatKey);
    const decryptedText = decrypt.toString(CryptoJS.enc.Utf8);
    let parsedData = JSON.parse(decryptedText)
    return parsedData
}

async function unzip(data){
    return new Promise((reslove,reject)=>{
        let fileName = data[0].originalname.split(".")
        const bufferData = Buffer.from(data[0].buffer, 'base64');
        const bufferStream = new require('stream').Readable();
        bufferStream.push(bufferData);
        bufferStream.push(null);
        const outputPath = path.join(__dirname,"/images/imagesUpload")
        if (!fs.existsSync(outputPath)) {
            fs.mkdirSync(outputPath, { recursive: true });
        }
        bufferStream.pipe(unzipper.Extract({ path: outputPath }))
            .on('finish', () => {
                reslove(fileName[0])
            })
            .on('error', err => {
                reject(err)
            });
    }) 
}

function checkImageExists(imagePath) {
    try {
        if (fs.existsSync(imagePath)) {
            return true;
        } else {
            return false;
        }
    } catch (error) {
        return false;
    }
}

function imageToBuffer(imagePath) {
    return new Promise((resolve, reject) => {
        fs.readFile(imagePath, (err, data) => {
            if (err) {
                reject(err);
            } else {
                resolve(data);
            }
        });
    });
}

function insertQueryBuilder(_jsonData) {
    return new Promise((resolve, reject) => {
        const columns = Object.keys(_jsonData[0]); 
        let columns1 = `(${columns.join(', ')})`
        let valuesPart = '';
        _jsonData.forEach((_obj, index) => {
            let values = Object.values(_obj).map(val => typeof val === 'string' ? `'${val}'` : val).join(', ');
            valuesPart += `(${values})`;
            if (index !== _jsonData.length - 1) {
                valuesPart += ', ';
            }
        });
        resolve({"columns":columns1,"values":valuesPart});
    });
}

async function otpGenerating(_mobile,_role) {
    let template = _role == 1 ? process.env.admin_mobile_templateId : process.env.user_mobile_templateId
    const url = `https://control.msg91.com/api/v5/otp?template_id=${template}&mobile=91${_mobile}&authkey=${process.env.authkey_otp}&realTimeResponse=1`;
    const headers = {
        "Content-Type": "application/json"
    };
    try {
        const response = await axios.post(url, {}, { headers });
        return response.data;
    } catch (error) {
        console.error('Error generating OTP:', error.message);
        throw error;
    }
}

async function ValidateOtp(_mobile,_otp) {
    const url = `https://control.msg91.com/api/v5/otp/verify?otp=${_otp}&mobile=91${_mobile}`;
    const headers = {
        "Content-Type": "application/json",
        "authkey": process.env.authkey_otp
    };
    try {
        const response = await axios.post(url, {}, { headers });
        return response.data;
    } catch (error) {
        throw error;
    }
}
module.exports = {
    QrcodeGenerating,
    QrcodeGeneratingmultiple,
    excelDataConvert,
    excel2Json,
    ImageUpload,
    validMobileno,
    decrypt,
    unzip,
    checkImageExists,
    imageToBuffer,
    insertQueryBuilder,
    otpGenerating,
    ValidateOtp
}