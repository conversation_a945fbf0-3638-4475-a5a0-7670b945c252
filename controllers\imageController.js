let imageController = function () { }

const message = require("./messages/ApiMessages")
const path = require("path")
const http = require("http")
const fs = require("fs")


imageController.get = async(req,res) => {
    try {
        const filePath = req.url.slice(1)
        const imagePath = path.join(__dirname, `/${filePath}`)
            fs.access(imagePath, fs.constants.F_OK, (err) => {
                if (err) {
                    res.writeHead(404, {'Content-Type': 'text/plain'});
                    res.end('File not found');
                } else {
                    fs.readFile(imagePath, (err, data) => {
                        if (err) {
                            res.writeHead(500, {'Content-Type': 'text/plain'});
                            res.end('Internal Server Error');
                        } else {
                            const extension = path.extname(imagePath).slice(1);
                            const contentType = `image/${extension}`;
                            res.writeHead(200, {'Content-Type': contentType});
                            res.end(data);
                        }
                    });
                }
            });
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

module.exports = imageController