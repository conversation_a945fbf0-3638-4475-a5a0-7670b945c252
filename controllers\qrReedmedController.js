const qr_reedmed = function () { }
const moment = require("moment")
const _ = require("underscore")
const lodash = require("lodash")
const queryBuilder = require("../db-config/queryBulding");
const message = require("./messages/ApiMessages")
const mapper = require("../db-config/connection")
const common = require("./commonController")
const { v4: uuidv4 } = require('uuid');


qr_reedmed.insert = async (req, res) => {
    try {
        let date = moment(new Date()).format("YYYY-MM-DD HH:MM:SS")
            let query = await queryBuilder("users", "findBy", { "id": req.usersData.user_id })
            if (query && query.length > 0) {
                let reedmedLimit = await queryBuilder("coins_conversion", "findBy", { "role_id": query[0].role_id })
                let filterColumns = "SUM(reward_points) AS reward_points"
                let scannedData = await queryBuilder("scanned_qr", "findBy", { "user_id": req.usersData.user_id, "is_approved": 0 })
                let scannedQr = await queryBuilder("scanned_qr", "findBy", { "user_id": req.usersData.user_id, "is_approved": 0 }, filterColumns)
                let guid = uuidv4();
                if (scannedData && scannedData.length > 0) {
                    if (query[0]["role_id"] == 2) {
                        if (scannedQr[0].reward_points >= reedmedLimit[0].reedmed_limit) {
                            let promises = scannedData.map(async function (data) {
                                let params = {
                                    "qr_list_id": data.qr_list_id,
                                    "approvals": 0,
                                    "scanned_date": dateFormate,
                                    "redeemed_by": req.usersData.user_id,
                                    "redeemed_points": data.reward_points,
                                    "location": req.body.location || "",
                                    "transaction_id":guid
                                }
                                await mapper(`UPDATE scanned_qr SET is_approved = 2 WHERE is_approved = 0 AND user_id = ${req.usersData.user_id}`)
                                await mapper(`UPDATE generated_qr_list SET is_redeemed = 1 WHERE is_redeemed = 0 AND id = ${data.qr_list_id}`)
                                await queryBuilder("redeemed_qr", "insert", params)
                            })
                            let params1 = {
                                "user_id":req.usersData.user_id,
                                "product_id ":"",
                                "reward_points": `-${scannedQr[0].reward_points}`,
                                "scanned_dt":""
                            }
                            await queryBuilder("transaction_history", "insert", params1)
                            await Promise.all(promises)
                            return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.insert })
                        }
                        else {
                            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.enoughPoints })
                        }
                    }
                    else if (query[0]["role_id"] == 3) {
                        let promises = scannedData.map(async function (data) {
                            let dateFormate = moment(data.scanned_date).format("YYYY-MM-DD HH:MM:SS")
                            let params = {
                                "qr_list_id": data.qr_list_id,
                                "approvals": 0,
                                "scanned_date": dateFormate,
                                "redeemed_by": req.usersData.user_id,
                                "redeemed_points": data.reward_points,
                                "location": req.body.location || "",
                                "transaction_id":guid
                            }
                            await queryBuilder("redeemed_qr", "insert", params)
                            await mapper(`UPDATE scanned_qr SET is_approved = 2 WHERE is_approved = 0 AND user_id = ${req.usersData.user_id}`)
                            await mapper(`UPDATE generated_qr_list SET is_redeemed = 1 WHERE is_redeemed = 0 AND id = ${data.qr_list_id}`)
                        })
                        let params1 = {
                            "user_id":req.usersData.user_id,
                            "product_id ":"",
                            "reward_points": `-${scannedQr[0].reward_points}`,
                            "scanned_dt":""
                        }
                        await queryBuilder("transaction_history", "insert", params1)
                        await Promise.all(promises)
                        return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.insert })
                    }
                    else {
                        return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.noRoleFound })
                    }
                }
                else {
                    return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.noreedmedPoints })
                }
            }
            else {
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validuserId })
            }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

qr_reedmed.get = async (req, res) => {
    try {
            let filterColumns = "redeemed_qr.*,SUM(redeemed_points) AS total_redeem_points,products.name AS product_name,users.name As user_name,roles.name AS role_name"
            let adding;
            if(req.body.from_date && req.body.to_date){
                adding = `WHERE approvals =0 AND DATE(redeemed_date) BETWEEN '${req.body.from_date}' AND '${req.body.to_date}' GROUP BY transaction_id`
            }
            else{
                adding = `WHERE approvals =0 GROUP BY transaction_id`
            }
            let joins = {
            "addingQuery": adding,
            "query": [
                {
                    "join": "JOIN",
                    "tableName": "generated_qr_list,redeemed_qr",
                    "columns": "id =,qr_list_id",
                },
                {
                    "join": "JOIN",
                    "tableName": "users,redeemed_qr",
                    "columns": "id =,redeemed_by", 
                },
                {
                    "join": "JOIN",
                    "tableName": "roles,users",
                    "columns": "id =,role_id", 
                },
                {
                    "join": "JOIN",
                    "tableName": "products,generated_qr_list",
                    "columns": "id =,product_id",
                }
            ]
        };
        let query = await queryBuilder("redeemed_qr", "find","",filterColumns,joins)
        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query, "message": "" })
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

module.exports = qr_reedmed