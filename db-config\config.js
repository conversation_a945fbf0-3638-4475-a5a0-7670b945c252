require('dotenv').config();
let port = process.env.PORT
let ENV = "DEV" //TEST,PROD
let params = {}

switch (ENV) {
    case "DEV":
        params = {
            host: '127.0.0.1',
            user: 'root',
            password: '', // Add your MySQL password here
            database: 'dilips_india'
        }
        break;
    case "PROD":
        params = {
            host: '127.0.0.1',
            user: 'root',
            password: '', // Add your MySQL password here
            database: 'dilips_india'
        }
        break;
    case "TEST":
        params = {
            host: '127.0.0.1',
            user: 'root',
            password: '', // Add your MySQL password here
            database: 'dilips_india'
        }
        break;
}

module.exports = {
    port,
    params
}