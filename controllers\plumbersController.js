const plumbers = function () { }
const multer = require("multer")
const _ = require("underscore")
const mapper = require("../db-config/connection")
const queryBuilder = require("../db-config/queryBulding");
const queryBuilderinsert = require("../db-config/queryBuldinginsert");
const common = require("./commonController")
const message = require("./messages/ApiMessages")

plumbers.get = async (req, res) => {
    try {
        let limit = {
            "page" : req.body.params.page ? parseInt(req.body.params.page) : 1,
            "limit" : 10
        }
        let filterData = {
            "role_id": 2
        }
        let filterColumns = "users.id,role_id,profile,email_id,users.name,mobile,roles.name AS role_name,users.location,DATE_FORMAT(users.created_at, '%d-%b-%Y') AS joined_dt, IFNULL(SUM(redeemed_qr.redeemed_points), 0) AS points_redeemed,IFNULL(SUM(scanned_qr.reward_points), 0) AS Accumulated_points"
        let joins = {
            "addingQuery": "WHERE role_id = 2 GROUP BY users.id",
            "query": [
                {
                    "join": "LEFT JOIN",
                    "tableName": "roles,users",
                    "columns": "id =,role_id",
                },
                {
                    "join": "LEFT JOIN",
                    "tableName": "redeemed_qr,users",
                    "columns": "redeemed_by =,id",
                },
                {
                    "join": "LEFT JOIN",
                    "tableName": "scanned_qr,users",
                    "columns": "user_id =,id AND scanned_qr.is_approved = 0",
                }
            ]
        }
        let countQuery = await mapper(`SELECT COUNT(*) AS total_count FROM users WHERE role_id = 2`);
        let query = await queryBuilderinsert("users", "find", filterData, filterColumns, joins,limit)
        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": query.data,"total_count":countQuery.data[0].total_count,"message": "" })
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

plumbers.plumbers_exports = async (req, res) => {
    try {
        let filterData = {
            "role_id": 2
        }
        let filterColumns = "name,email_id,profile,mobile,location"
        let query = await queryBuilder("users", "find", filterData, filterColumns)
        if (query && query.length > 0) {
            let resp = await common.excelDataConvert(query, res, "plumbers")
        } else {
            return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": [], "message": "" })
        }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

plumbers.upload = async (req, res) => {
    try {
        if (req.file) {
            console.log(req.file.mimetype)
            if (req.file.mimetype == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || req.file.mimetype == 'application/vnd.ms-excel') {
                let resp = await common.excel2Json(req.file, "users")
                if (resp && resp.length > 0) {
                    _.each(resp, async function (data, _idx) {
                        if (data && (data.mobile != null || data.mobile != undefined)) {
                            let respo = await queryBuilder("users", "findBy", { "mobile": data.mobile })
                            if (respo && respo.length == 0) {
                                data["role_id"] = 2
                                await queryBuilder("users", "insert", data)
                            }
                        }
                    })
                    return res.status(201).json({ "statusCode": 201, "status": "SUCCESS", "data": [], "message": message.insert })
                }
                else {
                    return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": [], "message": message.nodatainexcel })
                }
            }
            else {
                return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.excelFile })
            }
        } else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.noFile })
        }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "description": message.serverError })
    }
}


module.exports = plumbers