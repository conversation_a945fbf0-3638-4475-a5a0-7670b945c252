const transactions = function () { }
const queryBuilderlatest = require("../db-config/queryBuldinginsert");
const message = require("./messages/ApiMessages")
const _ = require("underscore")
const moment = require("moment")
const mapper = require("../db-config/connection")



transactions.get = async (req, res) => {
    try {
        let limit = {
            "page" : req.body.page ? parseInt(req.body.page) : 1,
            "limit" : 10
        }
        let filterColumns = "transaction_history.id,product_id,reward_points,DATE_FORMAT(transaction_history.created_at, '%d-%b-%Y') AS fromated_created_dt,IFNULL(products.name, 'Rewards Claimed') AS product_name "
        let joins = {
            "addingQuery": `WHERE user_id= ${req.usersData.user_id}`,
            "query": [
                {
                    "join": "LEFT JOIN",
                    "tableName": "products,transaction_history",
                    "columns": "id =,product_id",
                }
            ]
        }
        let countQuery = await mapper(`SELECT COUNT(*) AS total_count FROM transaction_history`);
        let transaction_history = await queryBuilderlatest("transaction_history", "find","", filterColumns,joins,limit)
        return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": transaction_history.data,"total_count":countQuery.data[0].total_count, "message": "" })
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

module.exports = transactions