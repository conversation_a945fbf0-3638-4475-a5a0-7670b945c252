let ApiMessages = function () { };

ApiMessages.serverError = "server Error",
ApiMessages.validParams = "Please Provide Valid Params" ,
ApiMessages.otpValidated = "Otp Validation Successfull!",
ApiMessages.otpValidation = "Please Provide Valid Otp",
ApiMessages.otpExpaired = "Otp Expired!",
ApiMessages.mobile = "Please Provide Valid Mobile No",
ApiMessages.mobileexists = "Mobile No already exists",
ApiMessages.mobileValidation = "Please Provide Valid Mobile No",
ApiMessages.generated = "Otp Generated Successfully!",
ApiMessages.usernotExists = "User Does Not Exists!",
ApiMessages.roleIdExists = "mobileNumber Exists with other Role",
ApiMessages.validuserId = "Please Provide valid UserId",
ApiMessages.unabletoRedmeed = "unable To Redeem"
ApiMessages.noRoleFound = "No Role Found",
ApiMessages.validetails = "Please fill inputs",
ApiMessages.invalidMobile = "Invalid mobile number",
ApiMessages.enoughPoints = "you don't have enough points to redeem",


/**common messages */
ApiMessages.noApi =  "no Api Found",
ApiMessages.insert =  "Data Saved Successfull!",
ApiMessages.update =  "Data Updated Successfull!",
ApiMessages.notFound =  "No Data Found",

/**token */
ApiMessages.notvalidToken = "Please Provide Valid Token",
ApiMessages.notprovide = "Please Provide Token"

/**excel files or images*/
ApiMessages.nodatainexcel = "No Data Exists in Excel File",
ApiMessages.excelFile = "Please Provide Valid xlsx file",
ApiMessages.files = "Please Provide Valid Excel and zip file",
ApiMessages.excelfileandzipFile = "Please Provide Excel and Zip File"
ApiMessages.noFile = "No File Found"

/**qr list */
ApiMessages.validQrListId = "Please Provide Valid qr",
ApiMessages.qrExpaired ="QR Code already claimed",
ApiMessages.qrNotfound = "Qr not found"

/**permission */
ApiMessages.permission = "You Dont have Permissions"

/**reedeemed points */
ApiMessages.noreedmedPoints = "you dont have redeemed points"

module.exports = ApiMessages