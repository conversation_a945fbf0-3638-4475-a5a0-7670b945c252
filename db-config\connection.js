const mysql = require("mysql")
const connection = require("./config")

const pool = mysql.createPool(connection.params);

module.exports = (sqlQuery) => {
    return new Promise((resolve, reject) => {
        pool.getConnection(function (err, connection) {
            if (err) {
                reject({ "data": err });
            } else {
                connection.query(sqlQuery, function (error, result) {
                    connection.release();
                    if (error) {
                        if (error.code == "ER_NO_SUCH_TABLE") {
                            reject({ "data": "Table Does Not Exists" });
                        }
                        else {
                            reject({ "data": error });
                        }
                    } else {
                        const lastInsertedId = result.insertId;
                        resolve({ "data": result,"lastInsertedId":lastInsertedId});
                    }
                });
            }
        });
    });
};