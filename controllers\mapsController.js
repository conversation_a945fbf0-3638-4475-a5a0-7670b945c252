let maps = function () { };
require('dotenv').config();
const axios = require("axios")
const message = require("./messages/ApiMessages")

maps.map = async (req, res) => {
    try {
        if (req.body.latitude && req.body.longitude) {
            const response = await axios.get(`https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${req.body.latitude}&lon=${req.body.longitude}`);
            return res.status(200).json({ "statusCode": 200, "status": "SUCCESS", "data": response.data, "message": "" })
        }
        else {
            return res.status(400).json({ "statusCode": 400, "status": "FAIL", "data": [], "message": message.validParams })
        }
    }
    catch (error) {
        return res.status(500).json({ "statusCode": 500, "status": "FAIL", "data": [], "message": message.serverError })
    }
}

module.exports = maps;