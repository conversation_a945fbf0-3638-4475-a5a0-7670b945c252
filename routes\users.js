const express = require('express');
require('dotenv').config();
const registeration = require("../controllers/registerationController")
const maps = require("../controllers/mapsController");
const token = require('../controllers/token');
const users = require("../controllers/usersController")
const scanner = require("../controllers/qrScannerController")
const products = require("../controllers/productsController")
const images = require("../controllers/imageController")
const reedemed = require("../controllers/qrReedmedController")
const transactions = require("../controllers/transactionsController")

const router = express();

/**registration */

router.route('/registeration').post(registeration.registeration);

router.route('/maps').post(maps.map);

/**image view */

router.route('/:imagePath/:imageSubPath/:image').get(images.get)

/**token verification */
router.all("*/",token.verify_Token)

router.route('/getUserDetails').post(users.get);

router.route('/user-update').post(registeration.update);

/**scanner */

router.route('/scanner/store').post(scanner.insert)

router.route('/scanners/get').post(scanner.approvalsById)

/**reedmed */

router.route('/reedemed/store').post(reedemed.insert)

/**products get By id */

router.route('/products/get').post(products.usersProductById)

/**transactions */

router.route('/transactions/get').post(transactions.get)

router.all('*',token.no_api)

module.exports = router;